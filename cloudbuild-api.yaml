steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'us-central1-docker.pkg.dev/coherent-brand-461002-t0/omnivore-services/api:$COMMIT_SHA'
      - '-f'
      - 'packages/api/Dockerfile'
      - '.'
    id: 'Build Docker Image'

  # Push the Docker image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'us-central1-docker.pkg.dev/coherent-brand-461002-t0/omnivore-services/api:$COMMIT_SHA'
    id: 'Push Docker Image'
    waitFor: ['Build Docker Image'] # Ensure build is complete before pushing

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'api-service' # Name of the Cloud Run service
      - '--image=us-central1-docker.pkg.dev/coherent-brand-461002-t0/omnivore-services/api:$COMMIT_SHA'
      - '--region=us-central1'
      - '--platform=managed'
      - '--allow-unauthenticated' # As per requirement
      - '--service-account=<EMAIL>'
      - '--vpc-connector=omnivore-vpc-connector' # VPC connector for private network access
      - '--project=coherent-brand-461002-t0'
      - '--set-env-vars=NODE_ENV=production,DB_HOST=************,DB_USER=postgres,DB_NAME=omnivore,REDIS_HOST=**********,REDIS_PORT=6378'
      - '--update-secrets=DB_PASSWORD=omnivore-db-password:latest,REDIS_AUTH_STRING=omnivore-redis-auth-string:latest'
    id: 'Deploy to Cloud Run'
    waitFor: ['Push Docker Image'] # Ensure image is pushed before deploying

# Specify the image to be built and pushed
images:
  - 'us-central1-docker.pkg.dev/coherent-brand-461002-t0/omnivore-services/api:$COMMIT_SHA'

# Optional: Set a timeout for the build (e.g., 20 minutes)
# timeout: '1200s'